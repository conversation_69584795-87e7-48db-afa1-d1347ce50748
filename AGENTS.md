# 开发规范

## Code style

- 使用中文
- 禁止硬编码，使用常量或枚举
- 方法 ≤40 行，类 ≤400 行
- 单行方法也使用多行大括号
- 清理无用代码(方法、参数、导入、依赖)
- 无冲突时使用简写类名

## Data access

- 禁用 MyBatis-Plus Wrapper 查询
- 仅使用 baseMapper 基本方法和自定义 mapper 方法

## Error handling

- 使用@jakarta.annotation.Nonnull/@Nullable 注解
- 除导入时数据验证的异常，其他异常需使用常量定义 key，并以中文形式声明在 resources/message.properties 多语言文件中
- 导入时的数据验证异常，需以中文形式声明在 resources/i18n.properties 多语言文件中

## Development process

- 复杂逻辑如不能在本次会话中实现，请务必标注 TODO 并说明后续实现思路

## Testing instructions

- 无需编写任何单元测试和集成测试
- 无需编译验证项目代码，如调用终端运行类似如下命令：_mvn compile_
