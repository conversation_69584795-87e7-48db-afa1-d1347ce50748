package com.yxt.talent.rv.application.todo;

import cn.hutool.core.collection.ListUtil;
import com.yxt.common.Constants;
import com.yxt.msgfacade.bean.GroupUserInfo;
import com.yxt.msgfacade.bean.Todo4Create;
import com.yxt.msgfacade.bean.Todo4Modify;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.po.calimeet.CalimeetParticipantsPO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 校准会待办 策略器
 */
@Component
public class CaliMeetSceneStrategy extends
        TodoSceneStrategy<CaliMeetSceneStrategy.CaliMeetTodoInfoDto, CaliMeetSceneStrategy.CaliMeetTodoInfoDto> {

    private final String PARAM_CALI_MEET_NAME = "{{calibrationName}}";

    private final String PARAM_CALI_NAME = "{{name}}";
    private final String PARAM_START_TIME = "{{startTime}}";
    private final String PARAM_END_TIME = "{{endTime}}";
    private final String PARAM_URL = "{{url}}";

    private final String CUSTOM_PARAM_NAME = "name";
    private final String CUSTOM_PARAM_BIZID = "bizIdL1";
    private final String CUSTOM_PARAM_TAGI18N = "tagI18n";

    public CaliMeetSceneStrategy() {
    }

    @Override
    public TodoSceneEnum getTodoSceneEnum() {
        return TodoSceneEnum.CALI_MEET_START;
    }

    @Override
    protected List<Todo4Create> convert2TodoCreates(String orgId, String opUserId, CaliMeetTodoInfoDto bizParam) {
        return bizParam.caliMeetUsers.stream().map(user -> {

            Map<String, String> params = getParams(bizParam.getMeetName(), bizParam.getStartTime(),
                    bizParam.getEndTime(), bizParam.getUrl());
            Map<String, Object> customParams = getParamsObj(bizParam.getMeetName(), bizParam.getMeetId());
            GroupUserInfo groupUserInfo = new GroupUserInfo();
            groupUserInfo.setOrgId(orgId);
            groupUserInfo.setUserIds(ListUtil.toList(user.getUserId()));

            Todo4Create create = new Todo4Create();
            create.setSceneCode(getSceneCode());
            create.setOrgId(orgId);
            create.setOrganizer(opUserId);
            create.setOperateUserId(opUserId);
            create.setTodoId(user.getCalimeetId());
            create.setParams(params);
            create.setGroupUserInfos(ListUtil.toList(groupUserInfo));
            create.setJumpUrl(bizParam.getUrl());
            create.setCustomParams(customParams);
            create.setStartTime(bizParam.getStartTime());
            create.setEndTime(bizParam.getEndTime());
            return create;
        }).collect(Collectors.toList());
    }

    private Map<String, Object> getParamsObj(String meetName, String bizId) {
        Map<String, Object> params = new HashMap<>();
        params.put(CUSTOM_PARAM_NAME, meetName);
        params.put(CUSTOM_PARAM_BIZID, bizId);
        params.put(CUSTOM_PARAM_TAGI18N, "talent_rv_cailmeet_i18n_key");
        return params;
    }

    @Override
    protected Todo4Modify convert2TodoInfo4ModifyItem(String orgId, String opUserId, CaliMeetTodoInfoDto bizParam) {
        Todo4Modify todo4Modify = new Todo4Modify();
        //机构id
        todo4Modify.setOrgIds(Collections.singletonList(orgId));
        //操作人
        todo4Modify.setOperateUserId(opUserId);
        //待办id
        todo4Modify.setTodoId(bizParam.getMeetId());
        //场景code
        todo4Modify.setSceneCode(getSceneCode());
        //跳转url
        todo4Modify.setJumpUrl(bizParam.getUrl());
        if (null != bizParam.getStartTime()) {
            todo4Modify.setStartTime(bizParam.getStartTime());
        }
        if (null != bizParam.getEndTime()) {
            todo4Modify.setEndTime(bizParam.getEndTime());
        }
        //自定义参数-模板替换
        Map<String, Object> customParams = getParamsObj(bizParam.getMeetName(), bizParam.getMeetId());
        todo4Modify.setCustomParams(customParams);
        Map<String, String> params = getParams(bizParam.getMeetName(), bizParam.getStartTime(), bizParam.getEndTime(),
                bizParam.getUrl());
        todo4Modify.setParams(params);
        return todo4Modify;
    }

    private Map<String, String> getParams(String meetName, LocalDateTime startTime, LocalDateTime endTime,
            String jumpUrl) {
        Map<String, String> params = new HashMap<>();
        params.put(PARAM_CALI_MEET_NAME, meetName);
        params.put(PARAM_CALI_NAME, meetName);
        if (Objects.nonNull(startTime)) {
            params.put(PARAM_START_TIME, DateTimeFormatter.ofPattern(Constants.SDF_YEAR2MINUTE).format(startTime));
        }
        if (Objects.nonNull(endTime)) {
            params.put(PARAM_END_TIME, DateTimeFormatter.ofPattern(Constants.SDF_YEAR2MINUTE).format(endTime));
        }
        params.put(PARAM_URL, jumpUrl);
        return params;
    }


    @NoArgsConstructor
    @AllArgsConstructor
    @Data
    public static class CaliMeetTodoInfoDto {

        private String meetName;

        private LocalDateTime startTime;

        private LocalDateTime endTime;

        private String url;

        private String meetId;

        /**
         * 校准会参与人
         */
        private List<CalimeetParticipantsPO> caliMeetUsers;

    }
}
