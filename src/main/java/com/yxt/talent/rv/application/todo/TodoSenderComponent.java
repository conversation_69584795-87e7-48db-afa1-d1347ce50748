package com.yxt.talent.rv.application.todo;

import cn.hutool.extra.spring.SpringUtil;

public class TodoSenderComponent {

    //获取ai访谈的待办消息维护策略对象
    public static CaliMeetSceneStrategy getCaliMeetSceneStrategy() {
        return SpringUtil.getBean(CaliMeetSceneStrategy.class);
    }

    public static AuthPrjStrategy getAuthPrjStrategy() {
        return SpringUtil.getBean(AuthPrjStrategy.class);
    }

}
