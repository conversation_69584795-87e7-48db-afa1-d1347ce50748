package com.yxt.talent.rv.application.todo;

public enum TodoSceneEnum {
    /**
     * 代办事项场景
     */
    CALI_MEET_START("talent_rv", "talent_rv_calibration_start", "启动校准任务时触发创建待办"),
    AUTH_PRJ_START("talent_rv", "talent_rv_certificate_start", "认证项目")
    ;
    private final String bizCode;

    private final String sceneCode;

    private final String name;

    TodoSceneEnum(String bizCode, String sceneCode, String name) {
        this.bizCode = bizCode;
        this.sceneCode = sceneCode;
        this.name = name;
    }

    public String getBizCode() {
        return bizCode;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public String getName() {
        return name;
    }
}
