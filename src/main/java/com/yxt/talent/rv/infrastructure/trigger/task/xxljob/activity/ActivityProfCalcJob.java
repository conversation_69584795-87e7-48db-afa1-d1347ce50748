package com.yxt.talent.rv.infrastructure.trigger.task.xxljob.activity;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.util.ShardingUtil;
import com.yxt.criteria.Result;
import com.yxt.event.EventPublisher;
import com.yxt.talent.rv.infrastructure.trigger.task.xxljob.activity.event.ActPerfCalculateTaskEvent;
import com.yxt.talent.rv.infrastructure.trigger.task.xxljob.activity.event.ActProfCalculateTaskEvent;
import com.yxt.task.Task;
import com.yxt.task.TaskHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 动态人员评估计算job 每隔一小时执行
 *
 * <AUTHOR>
 * @since 2024/12/27
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ActivityProfCalcJob implements TaskHandler<Task<String>> {

    private final EventPublisher eventPublisher;

    @XxlJob(value = "actProfCalculateJobHandler")
    public ReturnT<String> execute(String param) {
        Result<Void> taskResult = doHandle(Task.of(param));
        return taskResult.isSuccess() ? ReturnT.SUCCESS : ReturnT.FAIL;
    }

    @Override
    public Result<Void> handle(Task<String> task) {
        log.info("LOG09330:ActivityProfCalcJob execute");
        ShardingUtil.ShardingVO shardingVo = ShardingUtil.getShardingVo();
        ActProfCalculateTaskEvent event = new ActProfCalculateTaskEvent(shardingVo, task);
        eventPublisher.publish(event);

        ActPerfCalculateTaskEvent perfEvent = new ActPerfCalculateTaskEvent(shardingVo, task);
        eventPublisher.publish(perfEvent);
        return Result.success();
    }
}
