package com.yxt.talent.rv.infrastructure.trigger.message.rocket.authprj;

import com.yxt.talent.rv.application.authprj.AuthPrjCalcAppService;
import com.yxt.talent.rv.application.authprj.AuthPrjCalcTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.CONSUMER_GROUP_PREFIX;
import static com.yxt.talent.rv.infrastructure.common.constant.MQConstant.TOPIC_AUTH_PRJ_RESULT;

/**
 * 认证项目用户结果处理消费者
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RocketMQMessageListener(
    topic = TOPIC_AUTH_PRJ_RESULT,
    consumerGroup = CONSUMER_GROUP_PREFIX + TOPIC_AUTH_PRJ_RESULT,
    consumeThreadNumber = 3,
    consumeTimeout = 30)
public class AuthPrjResultConsumer implements RocketMQListener<AuthPrjResultMsg> {

    private final AuthPrjCalcAppService authPrjCalcAppService;
    private final AuthPrjCalcTaskService authPrjCalcTaskService;

    @Override
    public void onMessage(AuthPrjResultMsg msg) {
        String orgId = msg.getOrgId();
        String userId = msg.getUserId();
        String authPrjId = msg.getAuthPrjId();
        String traceId = msg.getTraceId();
        boolean forceClac = msg.isForceClac();
        boolean success = true;

        log.info("LOG09090:开始处理用户认证结果消息, userId={}, orgId={}, authPrjId={}, traceId={}", 
                userId, orgId, authPrjId, traceId);

        try {
            // 版本校验：如果不是强制计算，则检查消息中的traceId是否为当前活跃的计算ID
            if (!forceClac) {
                String currentCalcId = authPrjCalcTaskService.getCurrentCalcId(orgId, authPrjId);
                if (currentCalcId == null || !currentCalcId.equals(traceId)) {
                    log.info(
                        "LOG09100:消息版本已过期，跳过处理: userId={}, orgId={}, authPrjId={}, msgTraceId={}, currentCalcId={}",
                            userId, orgId, authPrjId, traceId, currentCalcId);
                    return; // 直接返回，不处理过期消息
                }
            }

            // 版本校验通过或强制计算，正常处理业务逻辑
            authPrjCalcAppService.processUserAuthResult(orgId, userId, authPrjId, traceId);
            log.info("LOG09110:用户认证结果处理完成, userId={}, orgId={}, authPrjId={}, traceId={}",
                    userId, orgId, authPrjId, traceId);

        } catch (Exception e) {
            log.error("LOG09120:处理用户认证结果消息失败, userId={}, orgId={}, authPrjId={}, traceId={}, error={}",
                    userId, orgId, authPrjId, traceId, e.getMessage(), e);
            success = false;
            // 根据业务需求决定是否需要重试，这里选择不抛出异常，避免MQ无限重试
        } finally {
            // 【修复竞态条件】只调用一次getCurrentCalcId，避免竞态条件
            String currentCalcId = authPrjCalcTaskService.getCurrentCalcId(orgId, authPrjId);
            boolean shouldDecrease = forceClac || (currentCalcId != null && currentCalcId.equals(traceId));
            
            if (shouldDecrease) {
                authPrjCalcTaskService.decreaseAndCompleteAsync(orgId, authPrjId, traceId, userId, success);
            } else {
                log.info("LOG10080:消息版本不匹配，跳过计数器递减: traceId={}, currentCalcId={}", 
                        traceId, currentCalcId);
            }
        }
    }
}
