package com.yxt.talent.rv.infrastructure.trigger.task.xxljob.xpd;

import com.alibaba.fastjson.JSON;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yxt.talent.rv.application.xpd.result.XpdResultCalcService;
import com.yxt.talent.rv.infrastructure.persistence.mybatis.mapper.xpd.XpdMapper;
import com.yxt.talent.rv.infrastructure.repository.xpd.XpdIdDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Description 绩效等级数据清洗
 *
 * <AUTHOR>
 * @Date 2024/7/8 10:23
 **/
@Component
@Slf4j
@RequiredArgsConstructor
public class RvXpdDataWashHandler {
    private final XpdResultCalcService xpdResultCalcService;
    private final XpdMapper xpdMapper;

    @XxlJob(value = "xpdFixDimRuleThreshold")
    public ReturnT<String> xpdFixDimRuleThreshold(String param) {
        if (StringUtils.isNotEmpty(param)) {
            param = param.trim();
            if (param.equalsIgnoreCase("all")) {
                for (XpdIdDto xpdIdDto : xpdMapper.allEndXpdIds(null)) {
                    doXpdFixDimRuleThreshold(xpdIdDto);
                }
            } else {
                for (String orgId : param.split(",")) {
                    orgId = orgId.trim();
                    if (StringUtils.isNotEmpty(orgId)) {
                        for (XpdIdDto xpdIdDto : xpdMapper.allEndXpdIds(orgId)) {
                            doXpdFixDimRuleThreshold(xpdIdDto);
                        }
                    }
                }
            }
        }
        return ReturnT.SUCCESS;
    }

    private void doXpdFixDimRuleThreshold(XpdIdDto xpdIdDto) {
        try {
            xpdResultCalcService.xpdFixDimRuleThreshold(xpdIdDto.getOrgId(), xpdIdDto.getId());
        } catch (Exception e) {
            log.error("LOG09340:doXpdFixDimRuleThreshold failed xpdIdDto {}", JSON.toJSONString(xpdIdDto), e);
        }
    }
}
